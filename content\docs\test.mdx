---
title: Test Page
---

# Test Page

<div className="bg-fd-background rounded-lg prose-no-margin">

```php title="routes/web.php"
// [!code word:Eid]
Route::get('/Eid', function () {
    return 'Eid <PERSON>';
});

```

<Tabs items={['TypeScript ', 'PHP']}>
  <Tab value="TypeScript">TypeScript is weird</Tab>
  <Tab value="PHP">PHP is fast</Tab>
</Tabs>

import { Accordion, Accordions } from 'fumadocs-ui/components/accordion';

<Accordions type="single">
  <Accordion title="My Title">My Content</Accordion>
</Accordions>
</div>


<div>

# Button Component

Our `Button` component is designed for various actions and interactions.

## Default Button

This is a basic button example.

<Button>Click Me</Button>

```tsx
import { Button } from '@/components/ui/button';

<Button>Click Me</Button>
```

</div>