{"name": "jerseydoc", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fumadocs-core": "15.5.0", "fumadocs-mdx": "11.6.6", "fumadocs-typescript": "^4.0.5", "fumadocs-ui": "15.5.0", "lucide-react": "^0.524.0", "next": "15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "@types/mdx": "^2.0.13", "@types/node": "22.15.28", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.5", "postcss": "^8.5.4", "tailwindcss": "^4.1.8", "tw-animate-css": "^1.3.4", "typescript": "^5.8.3"}}